import { Grid, styled } from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { PATHS } from 'constants';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import LeftArrowButton from 'components/LeftArrowButton/LeftArrowButton';
import RightArrowButton from 'components/RightArrowButton/RightArrowButton';
import { useRef } from 'react';
import { useIsDesktop } from 'utils';
import newestImg from '../../statics/imgs/newest.png';
import mostlyViewedImg from '../../statics/imgs/mostly_viewed.png';
import mostlyLikedImg from '../../statics/imgs/mostly_liked.png';
import electedImg from '../../statics/imgs/elected.png';
// import likeRequestImg from '../../statics/imgs/like_request.png';
// import followRequestImg from '../../statics/imgs/follow_request.png';
import followRequestImg from '../../statics/imgs/war-room.png';
// import commentRequestImg from '../../statics/imgs/comment_request.png';
import evaluateImg from '../../statics/imgs/evaluate.png';
import marfookImg from '../../statics/imgs/marfook.png';

const MyImg = styled('img')(() => ({
  width: '100%',
  borderRadius: 8,
  objectFit: 'cover',
  objectPosition: 'bottom',
}));

const getImages = (
  isLoggedIn,
  isAdmin,
  isEvaluator,
  isMarfookAdmin,
  isMarfookExpert,
) => {
  const thirtyDaysBefore = dayjs()
    .subtract(30, 'day')
    .startOf('day')
    .format('YYYY-MM-DD');

  const images = [];

  if (isEvaluator) {
    images.push({
      image: evaluateImg,
      url: `${PATHS.search}?evaluation_requested=on`,
    });
  }

  if (isMarfookAdmin || isMarfookExpert) {
    images.push({
      image: marfookImg,
      url: '/content-marfook-admin',
    });
  }

  if (isLoggedIn) {
    images.push({
      image: electedImg,
      url: '/content-marfook',
    });
  }

  if (isLoggedIn) {
    images.push({
      image: followRequestImg,
      url: `${PATHS.search}?support_request=on`,
    });
  }

  if (!(isMarfookAdmin || isMarfookExpert)) {
    images.push(
      ...[
        {
          image: mostlyLikedImg,
          url: `${PATHS.search}?ordering=-likes_count&created_at__gt=${thirtyDaysBefore}`,
        },
        {
          image: mostlyViewedImg,
          url: `${PATHS.search}?ordering=-views_count&created_at__gt=${thirtyDaysBefore}`,
        },
        {
          image: newestImg,
          url: PATHS.search,
        },
      ],
    );
  }

  return images;
};

function ArrowContainer({ isRight, children }) {
  const gradientDegree = isRight ? '-90deg' : '90deg';

  return (
    <Grid
      item
      sx={{
        position: 'absolute',
        display: 'flex',
        height: '100%',
        backgroundImage: `linear-gradient(${gradientDegree}, black, transparent)`,
        left: isRight ? 0 : 'none',
        right: isRight ? 'none' : 0,
      }}
    >
      {children}
    </Grid>
  );
}

export default function VerticalBanners() {
  const me = useSelector(selectMe);
  const scrollContainer = useRef(null);
  const scrollSize = 160; // Does not matter the value.

  const isDesktop = useIsDesktop();
  const isLoggedIn = !!me;
  const isAdmin = !!me?.can_see_admin_panel;
  const isEvaluator = !!me?.groups?.includes('MarfookEvaluator');
  const isMarfookExpert = !!me?.groups?.includes('MarfookExpert');
  const isMarfookAdmin = !!me?.groups?.includes('MarfookManager');

  return (
    <Grid
      container
      item
      sx={{ mt: isDesktop ? 7 : 1, position: 'relative', overflow: 'hidden' }}
      lg={6}
      xs={12}
      wrap="nowrap"
      flexDirection="row"
      alignItems="center"
    >
      {isLoggedIn && (
        <ArrowContainer isRight>
          <RightArrowButton
            scrollContainer={scrollContainer}
            scrollSize={scrollSize}
          />
        </ArrowContainer>
      )}

      <Grid
        container
        item
        justifyContent="space-between"
        alignItems="center"
        spacing={2}
        sx={{ overflowX: 'scroll' }}
        wrap="nowrap"
        ref={scrollContainer}
        flexGrow={0}
        className="no-scrollbar"
      >
        {getImages(
          isLoggedIn,
          isAdmin,
          isEvaluator,
          isMarfookAdmin,
          isMarfookExpert,
        ).map(image => (
          <Grid item xs={4} flexShrink={0}>
            <MyLink to={image.url}>
              <MyImg src={image.image} />
            </MyLink>
          </Grid>
        ))}
      </Grid>

      {isLoggedIn && (
        <ArrowContainer>
          <LeftArrowButton
            scrollContainer={scrollContainer}
            scrollSize={scrollSize}
          />
        </ArrowContainer>
      )}
    </Grid>
  );
}

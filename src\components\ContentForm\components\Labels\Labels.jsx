import {
  <PERSON>,
  Grid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>Field,
  styled,
} from '@mui/material';
import { useEffect, useState } from 'react';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import { trimWhiteSpaces } from 'utils';

const HiddenInput = styled('input')({ display: 'none' });

export default function Labels({
  defaultValue = [],
  disabled = false,
  externalHashtags = '',
}) {
  const [labels, setLabels] = useState([]);
  const [value, setValue] = useState('');

  useEffect(() => {
    setLabels(defaultValue);
  }, []);

  // Update labels when external hashtags change
  useEffect(() => {
    if (externalHashtags) {
      const newLabels = externalHashtags
        .split(' ')
        .map(tag => tag.replace('#', ''))
        .filter(label => label.length > 0);

      if (newLabels.length > 0) {
        setLabels(prevLabels => {
          // Create a Set with existing labels and new labels to remove duplicates
          const uniqueLabels = new Set([...prevLabels, ...newLabels]);
          return Array.from(uniqueLabels);
        });
      }
    }
  }, [externalHashtags]);

  const removeSharp = inStr => inStr.replaceAll('#', '');

  const addLabel = () => {
    if (trimWhiteSpaces(value)) {
      const newLabels = value
        .split(' ')
        .map(removeSharp)
        .filter(label => label.length > 0);

      if (newLabels.length > 0) {
        setLabels(prevLabels => {
          // Create a Set with existing labels and new labels to remove duplicates
          const uniqueLabels = new Set([...prevLabels, ...newLabels]);
          return Array.from(uniqueLabels);
        });
        setValue('');
      }
    }
  };

  const setLabel = text => {
    setValue(text.replaceAll(' ', '_'));
  };

  const handlePaste = e => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const newLabels = pastedText
      .split(' ')
      .map(removeSharp)
      .filter(label => trimWhiteSpaces(label).length > 0);

    if (newLabels.length > 0) {
      setLabels(prevLabels => {
        // Create a Set with existing labels and new labels to remove duplicates
        const uniqueLabels = new Set([...prevLabels, ...newLabels]);
        return Array.from(uniqueLabels);
      });
      setValue('');
    }
  };

  return (
    <>
      <Stack direction="row" alignItems="center" sx={{ mt: 2 }}>
        <TextField
          fullWidth
          value={value}
          onChange={e => setLabel(e.target.value)}
          label="# هشتگ"
          onPaste={handlePaste}
          disabled={disabled}
        />

        <IconButton onClick={addLabel}>
          <ControlPointIcon />
        </IconButton>
      </Stack>
      <Grid container gap={1} sx={{ mt: 1 }}>
        {labels.map(label => (
          <Chip
            label={label}
            key={label}
            deleteIcon={<DeleteOutlineOutlinedIcon />}
            onDelete={() => setLabels(labels.filter(value => value !== label))}
          />
        ))}
      </Grid>

      <HiddenInput name="labels" value={labels.join(',')} readOnly />
    </>
  );
}
